/**
 * 注册表单组件
 */

import Vue from "vue";
import system from "@/common/system";

// 注册通用表单组件
const components = require.context("@/components/form", true, /.vue$/);

let map = {};

components.keys().forEach((fileName) => {
  let component = components(fileName);
  const compPath = fileName.split("/");
  map[compPath[compPath.length - 2]] = component.default;
});

Object.keys(map).forEach((key) => {
  Vue.component(key, map[key]);
});

// 注册分包自定义组件
system.forEach((item) => {
  const systemKey = item.key;
  const currentSystemKey = uni.getStorageSync("system_key");

  // 只处理当前激活的系统
  if (currentSystemKey && systemKey !== currentSystemKey) return;

  try {
    if (!item.customFields) return;

    // 使用固定路径扫描所有分包组件
    const allSubPackageComponents = require.context("@/", true, /pages_\w+\/components\/Field\/.+\.vue$/);

    let subPackageMap = {};

    allSubPackageComponents.keys().forEach((fileName) => {
      // 检查是否属于当前系统
      if (!fileName.includes(`pages_${systemKey}/`)) {
        return;
      }

      let component = allSubPackageComponents(fileName);
      const pathParts = fileName.split("/");

      // 找到 Field 目录后的组件文件夹名称
      const fieldIndex = pathParts.findIndex((part) => part === "Field");
      if (fieldIndex !== -1 && fieldIndex + 1 < pathParts.length) {
        const componentName = pathParts[fieldIndex + 1];
        const registeredName = `${systemKey}-${componentName}`;
        subPackageMap[registeredName] = component.default;
      }
    });

    Object.keys(subPackageMap).forEach((key) => {
      console.log("已导入组件:", key);
      Vue.component(key, subPackageMap[key]);
    });
  } catch (error) {
    console.warn("导入组件失败:", error);
  }
});
