<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '数据填报',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col">
    <wd-navbar
      title="数据填报"
      placeholder
      safeAreaInsetTop
      leftArrow
      @click-left="navigateBack"
    ></wd-navbar>

    <scroll-view class="bg-gray-100" scroll-y>
      <view class="pb-12 bg-white">
        <GTForm v-if="options" :formUid="formUid" :options="options" ref="gtForm"></GTForm>
      </view>
    </scroll-view>
    <view class="btn-list flex" v-if="!readonly">
      <div class="flex-1 m-2">
        <wd-button type="info" @click="saveLocalHandler" block>暂存</wd-button>
      </div>
      <div class="flex-1 m-2">
        <wd-button type="primary" @click="submitHandler" :loading="submitLoading" block>
          立即提交
        </wd-button>
      </div>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useBasePage } from '@/pages/useBasePage'
import GTForm from '@/components/form/GTForm/index.vue'
import formDesign from '@/service/form/formDesign'
import formDataLocal from '@/service/local/formDataLocal'
import formData from '@/service/form/formData'
import { useUserStore, useStorageDataStore } from '@/store'
import { toApiFormData, toFrontFormData } from '@/utils/form'

const { navigateBack } = useBasePage()
const useStorageData = useStorageDataStore()

const gtForm = ref()
const formUid = ref(null)
const cacheRecordId = ref(null)
const formRecordId = ref(null)
const options = ref(null)
const submitLoading = ref(false)
const readonly = ref(false)

const { loading, run } = useRequest((formUid) => formDesign.getFormDef(formUid))

// 监听 loading 状态的变化
watch(loading, (newValue) => {
  if (newValue) {
    uni.showLoading({
      title: '加载中', // 替换为实际的加载提示文字
    })
  } else {
    uni.hideLoading()
  }
})

async function submitHandler() {
  submitLoading.value = true
  const res = await gtForm.value.submit()
  console.log(res)
  if (res.state) {
    const values = toApiFormData(res.data)
    if (values._id) {
      await updateFormData(formUid.value, values, cacheRecordId.value)
    } else {
      await addFormData(formUid.value, values, cacheRecordId.value)
    }
  } else {
    submitLoading.value = false
  }
}

async function addFormData(formUid, values, cacheRecordId) {
  try {
    await formData.addFormRecord(formUid, values, cacheRecordId)
    uni.showToast({
      icon: 'none',
      title: '数据已提交',
    })
    uni.navigateBack()
  } catch ({ msg, data, code }) {
    switch (code) {
      case 4011:
        break
      default:
        break
    }
  } finally {
    submitLoading.value = false
  }
}
async function updateFormData(formUid, values, cacheRecordId) {
  try {
    await formData.updateFormRecord(formUid, values, cacheRecordId)
    uni.showToast({
      icon: 'none',
      title: '数据已提交',
    })
    uni.navigateBack()
  } catch ({ msg, data, code }) {
    switch (code) {
      case 4011:
        break
      default:
        break
    }
  } finally {
    submitLoading.value = false
  }
}

async function saveLocalHandler() {
  const formData = await gtForm.value.submit()
  console.log(formData, 'saveLocalHandler')
  saveLocal(formData)
}

// 本地存储
async function saveLocal(formData) {
  if (!formData) return
  const cacheFormRecordId = await formDataLocal.upsertCacheRecord(
    formUid.value,
    cacheRecordId.value,
    formData,
  )
  if (cacheFormRecordId) {
    cacheRecordId.value = cacheRecordId
    uni.showToast({
      icon: 'none',
      title: '数据已缓存',
    })
    // 重置变化检测
  } else {
    console.log('数据缓存失败')
  }
}

onLoad(async (e) => {
  formUid.value = e.formUid
  cacheRecordId.value = e.cacheRecordId
  formRecordId.value = e.formRecordId
  console.log('🔥 :>> ', e)
  const res = await run(formUid.value)
  const opt = { formDef: res, formRecordData: {}, readonly: false }
  if (formRecordId.value) {
    // 获取表单数据
    // opt.formRecordId = formRecordId.value
    const result = useStorageData.getStorageData(formRecordId.value)
    opt.formRecordData = toFrontFormData(result)
  }
  if (cacheRecordId.value) {
    const result = useStorageData.getStorageData(cacheRecordId.value)
    opt.formRecordData = toFrontFormData(result)
  }
  if (e.readonly === '1') {
    readonly.value = true
    opt.readonly = true
  }
  console.log('opt :>> ', opt)
  options.value = opt
})
</script>

<style lang="scss" scoped></style>
