<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '数据列表',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col">
    <wd-navbar
      :title="title || '数据列表'"
      placeholder
      safeAreaInsetTop
      leftArrow
      @click-left="navigateBack"
    ></wd-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />
    <view class="info bg-white py-1 px-3 flex justify-between items-center text-xs">
      <view>共{{ total }}条</view>
      <view class="flex">
        <wd-tag
          custom-class="space"
          :type="createByMyself ? 'primary' : 'default'"
          @click="createByMyselfHandler"
          round
        >
          我的填报
        </wd-tag>
      </view>
    </view>
    <scroll-view class="bg-gray-100" scroll-y @scrolltolower="loadMoreData">
      <view class="pb-12">
        <div class="m-3" v-for="item in dataList" :key="item._id">
          <DataCard
            :data="item"
            :userInfo="userInfo"
            :config="cardConfig"
            @operation="operationHandler"
          ></DataCard>
        </div>
      </view>
    </scroll-view>
    <view class="position-absolute bottom-10 left-3" v-if="cacheDataCount">
      <wd-badge :modelValue="cacheDataCount">
        <wd-button size="small" @click="toCacheDataList">暂存数据</wd-button>
      </wd-badge>
    </view>
    <wd-fab position="right-bottom" :expandable="false" @click="addRecordHandler"></wd-fab>
  </view>
</template>

<script lang="ts" setup>
import formData from '@/service/form/formData'
import formDataLocal from '@/service/local/formDataLocal'
import DataCard from '@/components/common/DataCard/index.vue'
import SearchBar from '@/components/common/SearchBar/index.vue'
import { useBasePage } from '@/pages/useBasePage'
import { useUserStore, useStorageDataStore } from '@/store'
import { DEFAULT_CARD_CONFIG_OPERATIONS } from './list.config'
import { useToast } from '@/uni_modules/wot-design-uni'

const useStorageData = useStorageDataStore()
const useUser = useUserStore()
const userInfo = useUser.userInfo

const { show: showToast } = useToast()
const { navigateBack } = useBasePage()

defineOptions({
  name: 'formListPage',
})

let formUid
let pageLoaded = false
const pageParam = {
  pageNum: 1,
  pageSize: 10,
}

const title = ref()
const cardConfig = ref()
const searchConfig = ref()
const dataList = ref([])
const filterMap: Record<string, any> = {}
const total = ref()
const cacheDataCount = ref()
const createByMyself = ref(false)

const { loading, run } = useRequest((param) => formData.getFormRecords(formUid, param))

// 监听 loading 状态的变化
watch(loading, (newValue) => {
  if (newValue) {
    uni.showLoading({
      title: '加载中', // 替换为实际的加载提示文字
    })
  } else {
    uni.hideLoading()
  }
})

async function getDataList(reload?) {
  let filter = []

  Object.keys(filterMap).forEach((k) => {
    if (filterMap[k]) filter.push(filterMap[k])
  })

  if (filter.length >= 2) filter.unshift('and')

  if (filter.length === 1) filter = filter[0]

  if (!filter.length) filter = null

  const res = await run({ filter, page: pageParam })
  total.value = res.total
  if (reload) {
    dataList.value = [...res.list]
  } else {
    dataList.value.push(...res.list)
  }
}

async function loadMoreData() {
  console.log('loadMoreData')
  pageParam.pageNum += 1
  await getDataList()
}

async function reloadData() {
  pageParam.pageNum = 1
  await getDataList(true)
  pageLoaded = true
}

async function operationHandler({ key, data }) {
  console.log(key, data)
  switch (key) {
    case 'edit':
      editRecordHandler(data)
      break
    case 'view':
      viewRecordHandler(data)
      break
    case 'delete':
      deleteRecordHandler(data)
      break
    default:
      break
  }
}

async function searchChangeHandler(field, filter) {
  console.log(field, filter)
  filterMap[field] = filter
  await reloadData()
}

async function createByMyselfHandler() {
  createByMyself.value = !createByMyself.value
  console.log('userInfo :>> ', userInfo)
  filterMap.createBy = createByMyself.value ? ['=', 'create_by', userInfo.user_id] : null

  await reloadData()
}

async function getCacheData() {
  const res = await formDataLocal.countCacheRecords(formUid)
  console.log(res, 'cache')
  cacheDataCount.value = res
}

async function toCacheDataList() {
  useStorageData.setStorageData(formUid, cardConfig.value)
  uni.navigateTo({
    url: `/pages/cacheDataListPage/cacheDataListPage?formUid=${formUid}`,
  })
}

async function addRecordHandler() {
  uni.navigateTo({
    url: `/pages/formPage/formPage?formUid=${formUid}&cacheabl=false`,
  })
}

async function editRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages/formPage/formPage?formUid=${formUid}&formRecordId=${formRecordId}`,
  })
}

async function viewRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages/formPage/formPage?formUid=${formUid}&formRecordId=${formRecordId}&readonly=1`,
  })
}

async function deleteRecordHandler(record) {
  try {
    await formData.deleteFormDataById(formUid, record._id)
    await reloadData()
  } catch (error) {
    showToast('删除失败')
  }
}

onShow(async () => {
  if (formUid && pageLoaded) {
    await getCacheData()
    await reloadData()
  }
})

onLoad(async (e) => {
  console.log(e)

  formUid = e.formUid
  if (e.title) {
    title.value = e.title
  }
  const conf = useStorageData.getStorageData(formUid)
  console.log(conf)

  cardConfig.value = conf.card_config
    ? JSON.parse(conf.card_config)
    : { ...DEFAULT_CARD_CONFIG_OPERATIONS }

  // cardConfig.value = { ...DEFAULT_CARD_CONFIG_OPERATIONS }

  if (conf.search_config) {
    searchConfig.value = JSON.parse(conf.search_config)
    console.log(searchConfig.value)
  }
  await getCacheData()
  await reloadData()
})
</script>

<style lang="scss" scoped></style>
