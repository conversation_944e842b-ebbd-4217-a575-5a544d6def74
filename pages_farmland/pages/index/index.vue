<template>
  <view class="h-screen flex flex-col">
    <wd-navbar title="工作台" placeholder safeAreaInsetTop></wd-navbar>

    <scroll-view class="bg-gray-100" scroll-y>
      <view class="m-3" v-for="group in menuList" :key="group._id">
        <view class="task-group-title">
          <view class="title mb-2 font-bold text-gray-900">{{ group.title }}</view>
        </view>
        <view
          class="task-item p-4 bg-white flex items-center justify-between rounded mb-2"
          v-for="menu in group.app_workspace_child.data"
          :key="menu._id"
          @click="menuItemClick(menu)"
        >
          <view class="task-item-content">
            <view class="task-item-title">{{ menu.name }}</view>
          </view>
          <wd-icon name="arrow-right" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import formData from "@/service/form/formData";
import { WORKSPACE_TABLE, toFrontFormData } from "./config";
import { useStorageDataStore } from "@/store";

defineOptions({
  name: "Home",
});

const useStorageData = useStorageDataStore();

const { loading, run: init } = useRequest(() =>
  formData.getFormRecords(WORKSPACE_TABLE, {
    orders: [
      {
        field: "index",
        direction: "asc",
      },
    ],
  })
);

const menuList = ref([]);

onShow(async () => {
  const res = await init();
  menuList.value = res.list.map((item) => toFrontFormData(item));
});

const menuItemClick = (menu) => {
  if (menu.app_workspace_child_forms.data.length >= 2) {
    useStorageData.setStorageData(menu.name, menu.app_workspace_child_forms.data);
    uni.navigateTo({
      url: `/pages/workspaceMenu/workspaceMenu?menu=${menu.name}`,
    });
  } else if (menu.app_workspace_child_forms.data.length === 1) {
    const obj = menu.app_workspace_child_forms.data[0];

    useStorageData.setStorageData(obj.form_name, obj);
    if (obj.type === "preset") {
      uni.navigateTo({
        url: `/pages/taskFormListPage/taskFormListPage?formUid=${obj.form_name}&title=${obj.title}`,
      });
    } else {
      uni.navigateTo({
        url: `/pages/formListPage/formListPage?formUid=${obj.form_name}&title=${obj.title}`,
      });
    }
  }
};

// 监听 loading 状态的变化
watch(loading, (newValue) => {
  if (newValue) {
    uni.showLoading({
      title: "加载中", // 替换为实际的加载提示文字
    });
  } else {
    uni.hideLoading();
  }
});
</script>

<style lang="scss" scoped>
:deep(.loading-black) {
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}
</style>
