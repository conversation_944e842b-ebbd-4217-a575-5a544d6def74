<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '数据填报',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col">
    <wd-navbar
      title="数据填报"
      placeholder
      safeAreaInsetTop
      leftArrow
      @click-left="navBackClick"
    ></wd-navbar>

    <scroll-view class="bg-gray-100" scroll-y>
      <view class="pb-12 bg-white">
        <GTForm
          v-if="options"
          :formUid="formUid"
          :options="options"
          ref="gtForm"
          @init="gtFormInit"
        ></GTForm>
      </view>
    </scroll-view>
    <view class="btn-list flex" v-if="!readonly">
      <div class="flex-1 m-2" v-if="useCache">
        <wd-button type="info" @click="saveLocalHandler" block>暂存</wd-button>
      </div>
      <div class="flex-1 m-2">
        <wd-button type="primary" @click="submitHandler" :loading="submitLoading" block>
          立即提交
        </wd-button>
      </div>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useBasePage } from '@/pages/useBasePage'
import GTForm from '@/components/form/GTForm/index.vue'
import formDesign from '@/service/form/formDesign'
import formDataLocal from '@/service/local/formDataLocal'
import formData from '@/service/form/formData'
import bpmApi from '@/service/form/bpm'
import { useUserStore, useStorageDataStore } from '@/store'
import { toApiFormData, toFrontFormData } from '@/utils/form'
import * as turf from '@turf/turf'
import _ from 'lodash'

const { navigateBack } = useBasePage()
const useStorageData = useStorageDataStore()

const gtForm = ref()
const formUid = ref(null)
const useCache = ref(false)
const cacheRecordId = ref(null)
const pointId = ref('')
const formRecordId = ref(null)
const currentTask = ref(null)
const options = ref(null)
const submitLoading = ref(false)
const readonly = ref(false)

const { loading, run } = useRequest((formUid) => formDesign.getFormDef(formUid))

let defaultRecordData = {}

function gtFormInit(e) {
  defaultRecordData = { ...e }
}

function onBackPress(e) {
  if (e.from === 'backbutton') {
    navBackClick()
    return true
  }
}
function navBackClick() {
  const formData = _.omitBy(gtForm.value.getFormData, _.isNil)
  const defaultFormData = _.omitBy(defaultRecordData, _.isNil)
  console.log('formData :>> ', formData, defaultFormData)
  const same = _.isEqual(defaultFormData, formData)
  console.log('same :>> ', same)
  if (useCache.value && !same) {
    uni.showModal({
      title: '提示',
      content: `数据未保存，是否确定退出此页面？`,
      success: async (e) => {
        if (e.confirm) {
          navigateBack()
        }
      },
    })
  } else {
    navigateBack()
  }
}

// 监听 loading 状态的变化
watch(loading, (newValue) => {
  if (newValue) {
    uni.showLoading({
      title: '加载中', // 替换为实际的加载提示文字
    })
  } else {
    uni.hideLoading()
  }
})

async function submitHandler() {
  submitLoading.value = true
  const res = await gtForm.value.submit()
  console.log(res)
  if (res.state) {
    const values: any = {
      ...toApiFormData(res.data),

      pcode: currentTask.value.pointInfo.pcode,
      pname: currentTask.value.pointInfo.pname,
      ccode: currentTask.value.pointInfo.ccode,
      cname: currentTask.value.pointInfo.cname,
      fcode: currentTask.value.pointInfo.fcode,
      fname: currentTask.value.pointInfo.fname,
      point_code: currentTask.value.pointInfo.point_code,
      area_verified: currentTask.value.pointInfo.area_verified,
      area_reference: currentTask.value.pointInfo.area_reference,
    }

    const from = turf.point([values.sampling_longitude, values.sampling_latitude])
    const to = turf.point([
      currentTask.value.pointInfo.preset_longitude,
      currentTask.value.pointInfo.preset_latitude,
    ])

    const distance = turf.distance(from, to)
    console.log('distance :>> ', distance)
    if (distance > 0.2 && values.is_offset === '否') {
      uni.showModal({
        title: '提示',
        content: `当前样点现场坐标已超过预设点位200米，请填写偏移说明或编辑样点坐标`,
        showCancel: false,
        success: async (e) => {
          console.log('e :>> ', e)
          submitLoading.value = false
        },
      })
    } else {
      console.log('values :>> ', values)
      console.log('currentTask.value :>> ', currentTask.value)
      if (currentTask.value.sampling_task_type === '土壤') {
        values.sample_serial_number = `${currentTask.value.pointInfo.year}-${currentTask.value.point_code}-T`
      } else {
        values.sample_serial_number = `${currentTask.value.pointInfo.year}-${currentTask.value.point_code}-${values.agricultural_type_code}`
      }
      if (values._id) {
        await updateFormData(formUid.value, values, cacheRecordId.value)
      } else {
        await addFormData(formUid.value, values, cacheRecordId.value)
      }
      submitLoading.value = false
    }
  } else {
    submitLoading.value = false
  }
}

async function addFormData(formUid, values, cacheRecordId) {
  try {
    await formData.addFormRecord(formUid, values, cacheRecordId)
    let proccode
    if (currentTask.value.project_type === '省控监测采样项目') {
      proccode = '省控点审批流程'
    } else if (currentTask.value.project_type === '国控监测采样项目') {
      proccode = '国控点审批流程'
    } else {
      if (currentTask.value.project_fcode) {
        proccode = '县级审批流程'
      } else {
        proccode = '市级审批流程'
      }
    }
    await bpmApi.auditMisSubmit(proccode, 'sampling_task', currentTask.value._id, {
      audit_status: '已提交',
    })
    uni.showToast({
      icon: 'none',
      title: '数据已提交',
    })
    uni.navigateBack()
  } catch ({ msg, data, code }) {
    switch (code) {
      case 4011:
        break
      default:
        break
    }
  } finally {
    submitLoading.value = false
  }
}
async function updateFormData(formUid, values, cacheRecordId) {
  try {
    await formData.updateFormRecord(formUid, values, cacheRecordId)
    if (
      [
        '未填报',
        '采样单位已退回',
        '县级管理员已退回',
        '市级管理员已退回',
        '省级管理员已退回',
      ].indexOf(currentTask.value.audit_status) !== -1
    ) {
      let proccode
      if (currentTask.value.project_type === '省控监测采样项目') {
        proccode = '省控点审批流程'
      } else if (currentTask.value.project_type === '国控监测采样项目') {
        proccode = '国控点审批流程'
      } else {
        if (currentTask.value.project_fcode) {
          proccode = '县级审批流程'
        } else {
          proccode = '市级审批流程'
        }
      }
      await bpmApi.auditMisSubmit(proccode, 'sampling_task', currentTask.value._id, {})
    }

    uni.showToast({
      icon: 'none',
      title: '数据已提交',
    })
    uni.navigateBack()
  } catch ({ msg, data, code }) {
    switch (code) {
      case 4011:
        break
      default:
        break
    }
  } finally {
    submitLoading.value = false
  }
}

async function saveLocalHandler() {
  const formData = await gtForm.value.getFormData
  console.log('formData :>> ', formData)
  saveLocal(formData)
}

// 本地存储
async function saveLocal(formData) {
  if (!formData) return
  const cacheFormRecordId = await formDataLocal.upsertCacheRecord(
    formUid.value,
    cacheRecordId.value,
    formData,
    pointId.value,
  )
  if (cacheFormRecordId) {
    cacheRecordId.value = cacheRecordId
    uni.showToast({
      icon: 'none',
      title: '数据已缓存',
    })
    // 重置变化检测
  } else {
    console.log('数据缓存失败')
  }
}

onLoad(async (e) => {
  formUid.value = e.formUid
  const formDef = await run(formUid.value)
  const task = useStorageData.getStorageData(e.taskId)
  currentTask.value = task
  let recordData: any = {
    point_id: task.sampling_point_id,
  }
  const opt = {
    formDef,
    formRecordData: recordData,
    readonly: false,
  }

  console.log('task :>> ', task)
  const res = await formData.getFormRecords(formUid.value, {
    filter: ['=', 'point_id', task.sampling_point_id],
  })
  console.log('res :>> ', res)
  if (res.list && res.list.length) {
    recordData = toFrontFormData(res.list[0])
  } else {
    useCache.value = true
    pointId.value = task.sampling_point_id
    // const cac = await formDataLocal.getCacheRecords(formUid.value)
    // console.log('cac :>> ', cac)
    const cacheDataResult = await formDataLocal.getCacheRecord(
      formUid.value,
      task.sampling_point_id,
    )
    console.log('cacheDataResult :>> ', cacheDataResult)
    if (cacheDataResult) {
      cacheRecordId.value = task.sampling_point_id
      recordData = toFrontFormData(cacheDataResult.content)
    } else {
      const formMap = {
        土壤: 'ncpcjxxb',
        农产品: 'trcjxxb',
      }
      const mainRes = await formData.getFormRecords(formMap[task.sampling_task_type], {
        filter: ['=', 'point_id', task.sampling_point_id],
      })
      console.log('mainRes :>> ', mainRes)
      if (mainRes && mainRes.list && mainRes.list.length) {
        const obj = mainRes.list[0]
        const r = {
          point_id: obj.point_id,
          town: obj.town,
          village: obj.village,
          group_name: obj.group_name,
          land_type: obj.land_type,
          surrounding_east: obj.surrounding_east,
          surrounding_west: obj.surrounding_west,
          water_condition: obj.water_condition,
          current_season_disaster: obj.current_season_disaster,
          sampling_longitude: obj.sampling_longitude,
          sampling_latitude: obj.sampling_latitude,
          sampling_elevation: obj.sampling_elevation,
          sampling_address: obj.sampling_address,
          is_offset: obj.is_offset,
          offset_description: obj.offset_description,
          gps_device_display: obj.gps_device_display,
          center_east_direction: obj.center_east_direction,
          center_west_direction: obj.center_west_direction,
          center_south_direction: obj.center_south_direction,
          center_north_direction: obj.center_north_direction,
          ph_measurement_photo: obj.ph_measurement_photo,
          sampling_team: obj.sampling_team,
          sampling_team_leader: obj.sampling_team_leader,
          sampling_team_members: obj.sampling_team_members,
          nitrogen: obj.nitrogen,
          phosphorus: obj.phosphorus,
          potassium: obj.potassium,
          organic: obj.organic,
          compound: obj.compound,
          remarks: obj.remarks,
          surrounding_south: obj.surrounding_south,
          surrounding_north: obj.surrounding_north,
        }
        recordData = toFrontFormData(r)
      }
    }
  }

  // if (formRecordId.value) {
  //   // 获取表单数据
  //   // opt.formRecordId = formRecordId.value
  //   const result = useStorageData.getStorageData(formRecordId.value)
  //   opt.formRecordData = toFrontFormData(result)
  //   console.log('opt.formRecordData :>> ', opt.formRecordData)
  // }
  // if (cacheRecordId.value) {
  //   const result = useStorageData.getStorageData(cacheRecordId.value)
  //   opt.formRecordData = toFrontFormData(result)
  // }
  // if (e.readonly === '1') {
  //   readonly.value = true
  //   opt.readonly = true
  // }
  opt.formRecordData = {
    ...recordData,
    point_id: task.sampling_point_id,
    pcode: task.pointInfo.pcode,
    pname: task.pointInfo.pname,
    ccode: task.pointInfo.ccode,
    cname: task.pointInfo.cname,
    fcode: task.pointInfo.fcode,
    fname: task.pointInfo.fname,
    point_code: task.pointInfo.point_code,
    area_verified: task.pointInfo.area_verified,
    area_reference: task.pointInfo.area_reference,
  }

  options.value = opt
})
</script>

<style lang="scss" scoped></style>
