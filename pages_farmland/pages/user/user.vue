<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
  needLogin: true,
}
</route>
<template>
  <wd-navbar title="我的" safeAreaInsetTop></wd-navbar>
  <wd-cell-group>
    <wd-cell
      v-for="(item, index) in userList"
      :title="item.title"
      :value="getValue(item)"
      :icon="item.icon"
      :key="index"
    />
    <!-- #ifdef APP -->
    <wd-cell title="应用版本" :value="version" isLink @click="checkNewVersion"></wd-cell>
    <!-- #endif -->
  </wd-cell-group>
  <view class="logout px-4 mt-10">
    <wd-button type="primary" @click="logoutSubmit" block>退出登录</wd-button>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore, useTokenStore, useRefreshTokenStore } from '@/store'
import formData from '@/service/form/formData'

// #ifdef APP
import silenceUpdate from '@/uni_modules/rt-uni-update/js_sdk/silence-update' // 引入静默更新
// #endif

defineOptions({
  name: 'User',
})

interface UserItem {
  title: string
  value: string
  type?: string
  icon?: string
  customRender?: (data: any) => string
}

const userList = ref<UserItem[]>([
  {
    title: '姓名',
    value: 'nickname',
  },
  {
    title: '角色',
    value: 'role_name',
    type: 'roles',
    customRender: (roles) => {
      let str = ''
      if (roles && roles.length) {
        roles.forEach((i) => {
          str += '、' + i
        })
        str = str.replace('、', '')
      }
      return str
    },
  },
  {
    title: '邮箱',
    value: 'email',
  },
  {
    title: '电话',
    value: 'phone',
  },
  {
    title: '单位',
    value: 'sampling_unit',
  },
])

const logoutSubmit = () => {
  useUser.clearUserInfo()
  useTokenStore().clearToken()
  useRefreshTokenStore().clearToken()
  uni.redirectTo({ url: '/pages/login/login' })
}

const useUser = useUserStore()
const userInfo = useUser.userInfo

const getValue = (item) => {
  if (!item.type) {
    return userInfo[item.value]
  }
  if (item.type === 'roles') {
    return item.customRender(userInfo[item.value])
  }
}

// #ifdef APP
const VERSION_TEST = 'DEV.0.1'

const version = ref(VERSION_TEST)
try {
  plus.runtime.getProperty(plus.runtime.appid, (inf) => {
    version.value = inf.version
  })
} catch (e) {
  console.log(e)
}

const checkNewVersion = async () => {
  const res = await formData.getFormRecords('app_version_table', {})

  const data = {
    // 版本更新内容 支持<br>自动换行
    describe: '',
    edition_url: '', // apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx
    edition_force: 0, // 是否强制更新 0代表否 1代表是
    package_type: 1, // 0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
    edition_issue: 1, // 是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
    edition_number: 100, // 版本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）
    edition_name: '1.0.0', // 版本名称 manifest里的版本名称
    edition_silence: 0, // 是否静默更新 0代表否 1代表是
  }
  plus.runtime.getProperty(plus.runtime.appid, (inf) => {
    const version = inf.version
    if (res && res.list && res.list.length !== 0) {
      const infoData = res.list[0]
      if (infoData.edition_name !== version) {
        data.edition_number = infoData.edition_number
        data.edition_name = infoData.edition_name
        data.edition_url = infoData.edition_url
        data.edition_force = infoData.edition_force === '是' ? 1 : 0
        data.edition_silence = infoData.edition_silence === '是' ? 1 : 0
        data.edition_issue = infoData.edition_issue === '是' ? 1 : 0
        if (infoData.describe) data.describe = infoData.describe
        // 如果是wgt升级，并且是静默更新 （注意！！！ 如果是手动检查新版本，就不用判断静默更新，请直接跳转更新页，不然点击检查新版本后会没反应）
        if (data.package_type === 1 && data.edition_silence === 1) {
          // 调用静默更新方法 传入下载地址
          silenceUpdate(data.edition_url)
        } else {
          // 跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
          setTimeout(() => {
            uni.navigateTo({
              url:
                '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
                JSON.stringify(data),
            })
          }, 1000)
          console.log('跳转更新页面')
        }
      } else {
        uni.showToast({
          title: '已是最新版本',
        })
      }
    }
  })
}
// #endif
</script>

<style lang="scss" scoped></style>
