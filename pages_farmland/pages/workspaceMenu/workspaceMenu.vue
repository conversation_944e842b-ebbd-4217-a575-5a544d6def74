<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '菜单列表',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg">
    <wd-navbar
      :title="title"
      placeholder
      safeAreaInsetTop
      leftArrow
      @click-left="navigateBack"
    ></wd-navbar>
    <scroll-view scroll-y class="bg-gray-100">
      <view
        class="task-item p-4 m-3 bg-white flex items-center justify-between"
        v-for="project in projects"
        :key="project._id"
        @click="menuClickHandler(project)"
      >
        <view class="task-item-content">
          <view class="task-item-title">{{ project.project_name }}</view>
        </view>
        <wd-icon name="arrow-right" />
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import formData from '@/service/form/formData'
import { useBasePage } from '@/pages/useBasePage'
import { useStorageDataStore } from '@/store'
const { navigateBack } = useBasePage()

const useStorageData = useStorageDataStore()
const title = ref()
const projects = ref([])

const menuClickHandler = (e) => {
  console.log(e)
  // Storage.saveFormData(e.form_name, e)
  useStorageData.setStorageData(e._id, e)

  uni.navigateTo({
    url: `/pages/pointListPage/pointListPage?dataId=${e._id}&title=${e.project_name}`,
  })
}

const init = async () => {
  const res = await formData.getFormRecords('project_info', {
    filter: ['=', 'project_type', title.value],
  })
  projects.value = res.list
  console.log('res :>> ', res)
}

onLoad((e) => {
  title.value = e.menu
  init()
  console.log('e.menu :>> ', e.menu)
})
</script>

<style lang="scss" scoped></style>
